import { Injectable } from '@nestjs/common';

import { Users } from '@core/db/entities/users';
import { MinioService } from '@core/global/minio/minio.service';
import { PermissionValidatorService } from '@core/global/permission-validator/permission-validator.service';
import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';
import { TypeOrmOrderBy } from '@core/shared/common/common.typeorm';

import { GetReportsV1HttpParam } from './handler/dto/get-reports.v1.http.dto';
import { ReportsV1Repo } from './reports.v1.repo';
import {
  GetPaginateReports,
  GetReportDetail,
  ReportDetails,
} from './reports.v1.type';

@Injectable()
export class ReportsV1Service {
  constructor(
    private reportRepo: ReportsV1Repo,
    private minioService: MinioService,
    private permissionValidator: PermissionValidatorService,
  ) {}

  async getPaginateReports(
    user: Users,
    options: GetReportsV1HttpParam,
  ): Promise<Res<GetPaginateReports, ''>> {
    const query = {
      search: options.search,
      startDate: options.startDate,
      endDate: options.endDate,
      departmentIds: options.departmentIds,
      layoutDirections: options.layoutDirections,
      disasterIds: options.disasterIds,
      orderBy: options.orderBy || TypeOrmOrderBy.DESC,
      sortBy: options.sortBy || 'reviewedAt',
    };

    const { datas, pagination } = await this.reportRepo.getPaginateReports(
      {
        page: options.page,
        perPage: options.perPage,
      },
      query,
    );

    const transformedData = await Promise.all(
      datas.map(async (data) => {
        const { thumbnail, createdBy, ...other } = data;

        const thumbnailUrl = await this.minioService
          .getPresignedUrl({
            object: thumbnail?.resolutionLowPath || '',
          })
          .catch(() => '');

        const permission = {
          canDelete: this.permissionValidator.canDeleteReport({
            user,
            createdBy,
            documentStatus: other.status,
            department: other.department,
          }),
        };

        return {
          ...other,
          thumbnailUrl,
          permission,
        };
      }),
    );

    return Ok({
      datas: transformedData,
      pagination,
    });
  }

  async getReportDetailsById(
    id: number,
    user: Users,
  ): Promise<Res<GetReportDetail, 'notFound'>> {
    const template = await this.reportRepo.getReportDetailsById(id);

    if (!template) {
      return Err('notFound');
    }

    const permission = {
      canCreate: this.permissionValidator.canCreateReport({
        user,
        createdBy: template.createdBy,
        area: {
          id: template.responsibilityAreaId,
        },
      }),
      canEdit: this.permissionValidator.canEditReport({
        user,
        documentStatus: template.status,
        createdBy: template.createdBy,
        department: {
          id: template.createdBy.department.id,
        },
      }),
    };

    const transformedData: ReportDetails = {
      id: template.id,
      name: template.name,
      status: template.status,
      actionType: template.actionType,
      documentType: template.documentType,
      data: template.data,
      backgroundColor: template.backgroundColor,

      thumbnailUrl: await this.minioService
        .getPresignedUrl({
          object: template.thumbnail?.resolutionHighPath || '',
        })
        .catch(() => ''),

      layout: {
        id: template.layout.id,
        direction: template.layout.direction,
      },

      department: {
        id: template.createdBy.department?.id,
        nameTh: template.createdBy.department?.nameTh,
      },

      disasters: template.disasters?.map((disaster) => ({
        id: disaster.id,
        name: disaster.name,
      })),

      createdBy: {
        id: template.createdBy.id,
        firstname: template.createdBy.firstname,
        lastname: template.createdBy.lastname,
        isMadeByMe: user.id === template.createdBy.id,
      },

      reviewedBy: {
        id: template.reviewedBy?.id,
        firstname: template.reviewedBy?.firstname,
        lastname: template.reviewedBy?.lastname,
        isMadeByMe: user.id === template.reviewedBy?.id,
      },

      latestRequestedBy: {
        id: template.latestRequestedBy?.id,
        firstname: template.latestRequestedBy?.firstname,
        lastname: template.latestRequestedBy?.lastname,
        isMadeByMe: user.id === template.latestRequestedBy?.id,
      },

      latestEditedBy: {
        id: template.latestEditedBy?.id,
        firstname: template.latestEditedBy?.firstname,
        lastname: template.latestEditedBy?.lastname,
        isMadeByMe: user.id === template.latestEditedBy?.id,
      },

      createdAt: template.createdAt,
      reviewedAt: template.reviewedAt,
      latestRequestedAt: template.latestRequestedAt,
      latestEditedAt: template.latestEditedAt,

      permission,
    };

    return Ok({
      datas: transformedData,
    });
  }
}
