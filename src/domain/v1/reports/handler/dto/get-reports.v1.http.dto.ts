import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

import {
  transformToNumberArray,
  transformToStringArray,
} from '@core/shared/common/common.func';
import {
  TypeOrmOrderBy,
  TypeOrmOrderByType,
} from '@core/shared/common/common.typeorm';
import { PaginationResponseSchema } from '@core/shared/http/http.response.dto';
import { IStandardArrayApiResponse } from '@core/shared/http/http.standard';

import { LayoutDirection } from '@domain/v1/layouts/laytous.v1.constant';

import { ReportPaginateDetails } from '../../reports.v1.type';

export class GetReportsV1HttpParam {
  @ApiProperty({
    example: 1,
    type: Number,
  })
  @IsNumber()
  @Type(() => Number)
  page: number;

  @ApiProperty({
    example: 10,
    type: Number,
  })
  @IsNumber()
  @Type(() => Number)
  perPage: number;

  @ApiPropertyOptional({
    type: String,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    type: Date,
    example: '2025-06-16',
    description: 'Filter by start date (YYYY-MM-DD)',
  })
  @IsOptional()
  @Type(() => Date)
  startDate?: Date;

  @ApiPropertyOptional({
    type: Date,
    example: '2025-06-16',
    description: 'Filter by end date (YYYY-MM-DD)',
  })
  @IsOptional()
  @Type(() => Date)
  endDate?: Date;

  @ApiPropertyOptional({
    type: String,
    example: '[1,2,3]',
  })
  @IsOptional()
  @Transform(({ value }) => transformToNumberArray(value))
  departmentIds?: number[];

  @ApiPropertyOptional({
    type: String,
    example: '[1,2,3]',
  })
  @IsOptional()
  @Transform(({ value }) => transformToNumberArray(value))
  disasterIds?: number[];

  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    example: '[horizontal, vertical]',
    description: 'Filter by layout directions',
  })
  @Transform(({ value }) => transformToStringArray(value))
  @IsEnum(LayoutDirection, { each: true })
  layoutDirections?: string[];

  @ApiPropertyOptional({
    enum: ['name', 'reviewedAt', 'latestEditedAt', 'createdAt'],
    default: 'reviewedAt',
    example: 'reviewedAt',
  })
  @IsOptional()
  @IsEnum(['name', 'reviewedAt', 'latestEditedAt', 'createdAt'])
  sortBy?: string;

  @ApiPropertyOptional({
    enum: TypeOrmOrderBy,
    default: TypeOrmOrderBy.DESC,
    example: TypeOrmOrderBy.DESC,
  })
  @IsOptional()
  @IsEnum(TypeOrmOrderBy)
  orderBy?: TypeOrmOrderByType;
}

class GetReportsV1HttpData implements ReportPaginateDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  thumbnailUrl: string;
  backgroundColor: string;
  department: { id: number; nameTh: string };
  permission: { canDelete: boolean };
}

export class ReportsMetaResponse {
  pagination: PaginationResponseSchema;
}

export class GetReportsV1HttpResponse
  implements IStandardArrayApiResponse<ReportsMetaResponse>
{
  success: boolean;
  key: string;
  data: GetReportsV1HttpData[];
  meta: ReportsMetaResponse;
}
