import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';

import { ActivityLogs } from '@core/db/entities/activity-logs';
import { Invitations } from '@core/db/entities/invitations';
import { Users } from '@core/db/entities/users';
import { emailTemplate } from '@core/global/email/email.contants';
import { EmailService } from '@core/global/email/email.service';
import { hashString } from '@core/shared/common/common.crypto';
import tzDayjs from '@core/shared/common/common.dayjs';
import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';

import {
  ActivityLogAction,
  ActivityLogTargetType,
} from '../activity-logs/activity-logs.v1.constant';
import { UserStatus } from '../users/users.v1.constant';
import { UsersV1Repo } from '../users/users.v1.repo';
import {
  INVITE_SUBJECT,
  InvitationError,
  InviteStatus,
  TEN_DAYS,
} from './invitations.v1.constant';
import { InvitationsV1Repo } from './invitations.v1.repo';
import {
  CancelInvitation,
  InvitationErrorType,
  NewInvitation,
  VerifyifyInvitation,
} from './invitations.v1.type';

@Injectable()
export class InvitationsV1Service {
  constructor(
    private repo: InvitationsV1Repo,
    private userRepo: UsersV1Repo,
    private emailService: EmailService,
  ) {}

  async postInvitation(
    body: NewInvitation[],
    reqUser: Users,
  ): Promise<Res<any, InvitationErrorType>> {
    const uniqueDepartmentCodes = new Set(body.map((b) => b.departmentCode));
    const department = await this.repo.findDepartments({
      code: In(Array.from(uniqueDepartmentCodes)),
    });

    if (department.length !== uniqueDepartmentCodes.size)
      return Err(InvitationError.DepartmentNotFound);

    const invitedUsers = (await this._findOrCreateInviteUser(body)).filter(
      (user) => user !== null,
    );

    const invitations = await Promise.all(
      invitedUsers.map((user) =>
        this.repo.insertInvitation({
          userId: user.id,
          identifier: hashString(`${user.email}${tzDayjs().toString()}`),
          status: InviteStatus.Pending,
          expiredAt: new Date(Date.now() + TEN_DAYS),
        }),
      ),
    );

    await Promise.all(
      invitedUsers.map(async (user) => {
        const invitation = invitations.find(
          (i) => i.userId === user.id,
        ) as Invitations;

        const sendMailResult = await this.emailService.send({
          recipient: {
            email: user.email,
            firstname: user.firstname,
            lastname: user.lastname,
            token: invitation?.identifier || '',
          },
          subject: INVITE_SUBJECT,
          templateType: emailTemplate.INVITE,
        });

        const invitator = reqUser.firstname
          ? `${reqUser.firstname} ${reqUser.lastname}`
          : reqUser.email;

        await this.repo.insertActvityLog({
          action: ActivityLogAction.Invite,
          targetType: ActivityLogTargetType.User,
          targetId: user.id,
          description: `${invitator} ได้ทำการส่งคำเชิญ ${user.firstname} ${user.lastname} ไปยัง E-mail แล้ว`,
          userId: reqUser.id,
        } as ActivityLogs);

        await this.repo.updateInvitation({
          ...invitation,
          status: sendMailResult.isOk() ? InviteStatus.Sent : InviteStatus.Fail,
          ...(sendMailResult.isErr() && {
            error: sendMailResult.isErr() && sendMailResult.error.context,
          }),
        });
      }),
    );

    return Ok(null);
  }

  async postVerifyInvitation(
    body: VerifyifyInvitation,
  ): Promise<Res<any, InvitationErrorType>> {
    const invitation = await this.repo.findOneInvitationByIdentifier(
      body.token,
    );

    if (!invitation) return Err(InvitationError.InvitationNotFound);

    if (tzDayjs().isAfter(tzDayjs(invitation.expiredAt))) {
      await this.repo.updateInvitation({
        ...invitation,
        status: InviteStatus.Expired,
      });

      return Err(InvitationError.Expired);
    }

    await this.repo.updateInvitation({
      ...invitation,
      status: InviteStatus.Accepted,
      acceptAt: new Date(),
    });

    const user = await this.repo.findOneUser({ id: invitation.userId });

    await this.repo.updateUser({
      ...user,
      status: UserStatus.Active,
    } as Users);

    return Ok(null);
  }

  async patchCancelInvitation(
    body: CancelInvitation[],
  ): Promise<Res<null, InvitationErrorType>> {
    const users = await this.repo.findUsers({
      email: In(body.map(({ email }) => email)),
      status: UserStatus.Invited,
    });

    const invitations = await this.repo.findInvitations({
      userId: In(users.map((user: Users) => user.id)),
      status: In([InviteStatus.Pending, InviteStatus.Sent]),
    });

    if (invitations.length !== body.length) {
      return Err(InvitationError.InvitationNotFound);
    }

    const newInvitation = invitations.map((inv) => {
      inv.status = InviteStatus.Cancel;
      return inv;
    });

    await this.repo.updateInvitations(newInvitation);

    await this.repo.softDeleteUsers(users.map((user: Users) => user.id));

    return Ok(null);
  }

  async _findOrCreateInviteUser(
    users: NewInvitation[],
  ): Promise<(Users | null)[]> {
    return await Promise.all(
      users.map(async (user) => {
        const existedUser = await this.repo.findOneUser(
          { email: user.email},
          true,
        );

        const role = await this.repo.findOneRole({ name: user.role });

        if (existedUser) {
          if (
            existedUser.status === UserStatus.Active &&
            !existedUser.deletedAt
          ) {
            return null;
          }

          await this.repo.updateUser({
            ...existedUser,
            status: UserStatus.Invited,
            createdAt: tzDayjs().toDate(),
            deletedAt: null,
            userRoles: [
              ...existedUser.userRoles.map((ur, idx) => {
                if (idx === 0 && !!role?.id) {
                  return {
                    ...ur,
                    roleId: role.id,
                    role: role,
                  };
                }
                return ur;
              }),
            ],
          });

          const previousInvitations = await this.repo.findInvitations({
            userId: existedUser.id,
            status: In([InviteStatus.Pending, InviteStatus.Sent]),
          });

          await this.repo.updateInvitations(
            previousInvitations.map((invitation) => ({
              ...invitation,
              status: InviteStatus.Expired,
            })),
          );

          return existedUser;
        }

        const department = await this.repo.findOneDepartment({
          code: user.departmentCode,
        });

        const createdUser = await this.repo.insertUser({
          email: user.email,
          firstname: user.firstname,
          lastname: user.lastname,
          departmentId: department?.id,
          status: UserStatus.Invited,
        }, role);

        return createdUser
      }),
    );
  }
}
