import { Injectable } from '@nestjs/common';
import { FindOptionsWhere } from 'typeorm';

import { ActivityLogs } from '@core/db/entities/activity-logs';
import { Departments } from '@core/db/entities/departments';
import { Invitations } from '@core/db/entities/invitations';
import { Roles } from '@core/db/entities/roles';
import { Users } from '@core/db/entities/users';
import { BaseRepo } from '@core/shared/common/common.repo';

import { InviteStatus } from './invitations.v1.constant';
import { UserRoles } from '@core/db/entities/user-roles';
import { ApiException } from '@core/shared/http/http.exception';

@Injectable()
export class InvitationsV1Repo extends BaseRepo {
  async findOneUser(
    where: FindOptionsWhere<Users>,
    withDeleted = false,
  ): Promise<Users | null> {
    return await this.from(Users).findOne({
      where, relations: {
        userRoles: {
          role: true,
        },
      }, withDeleted
    });
  }

  async findUsers(
    where: FindOptionsWhere<Users> | FindOptionsWhere<Users>[],
    withDeleted = false,
  ): Promise<Users[] | []> {
    return await this.from(Users).find({ where, withDeleted });
  }

  async insertUser(data: Partial<Users>, role?: Roles | null): Promise<Users> {
    const user = this.from(Users).create(data);

    const result = await this.transaction(async () => {
      await this.from(Users).save(user);
      if (role) {
        await this.from(UserRoles).save({
          user,
          role,
        })
      }
      return user;
    })

    if (result.isErr()) {
      throw new ApiException(result.error, 500)
    }

    return user;
  }

  async updateUser(user: Users): Promise<void> {
    const { id, userRoles, ...data } = user;

    const result = await this.transaction(async () => {
      await this.from(Users).update(id, data);
      for (const userRole of userRoles) {
        await this.from(UserRoles).update(userRole.id, {
          roleId: userRole.role.id,
        });
      }
    });

    if (result.isErr()) {
      throw new ApiException(result.error, 500);
    }
  }

  async updateUsers(users: Users[]): Promise<void> {
    await this.from(Users).save(users);
  }

  async softDeleteUsers(userIds: number[]) {
    return await this.from(Users).softDelete(userIds);
  }

  async findOneDepartment(
    where: FindOptionsWhere<Departments>,
  ): Promise<Departments | null> {
    return await this.from(Departments).findOneBy(where);
  }

  async findDepartments(
    where: FindOptionsWhere<Departments>,
  ): Promise<Departments[]> {
    return await this.from(Departments).findBy(where);
  }

  async findOneRole(where: FindOptionsWhere<Roles>): Promise<Roles | null> {
    return await this.from(Roles).findOneBy(where);
  }

  async findRoles(where: FindOptionsWhere<Roles>): Promise<Roles[]> {
    return await this.from(Roles).findBy(where);
  }

  async findInvitations(
    where: FindOptionsWhere<Invitations> | FindOptionsWhere<Invitations>[],
  ): Promise<Invitations[]> {
    return await this.from(Invitations).findBy(where);
  }

  async insertInvitation(
    newInvitation: Partial<Invitations>,
  ): Promise<Invitations> {
    const invatation = this.from(Invitations).create(newInvitation);
    await this.from(Invitations).insert(invatation);

    return invatation;
  }

  async findOneInvitationByIdentifier(
    identifier: string,
  ): Promise<Invitations | null> {
    return this.from(Invitations).findOne({
      where: { identifier, status: InviteStatus.Sent },
      relations: { user: true },
    });
  }

  async updateInvitation(invitation: Invitations) {
    const { id, ...data } = invitation;
    const updated = await this.from(Invitations).update(id, data);
    return updated;
  }

  async updateInvitations(invitations: Invitations[]) {
    const updated = await this.from(Invitations).save(invitations);
    return updated;
  }

  async insertActvityLog(newActivityLog: ActivityLogs) {
    const activityLog = this.from(ActivityLogs).create(newActivityLog);
    await this.from(ActivityLogs).insert(activityLog);
  }
}
